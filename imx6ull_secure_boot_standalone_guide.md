# 手把手教你以 Standalone 方式编译i.MX6ULL的Secure Boot

## 📖 前言

本文档基于实际验证的方法，详细介绍如何在i.MX6ULL处理器上实现HABv4（High Assurance Boot version 4）安全启动。采用kernel和DTB统一签名的方式，确保启动镜像的完整性和真实性。

## 🎯 概述

### HAB安全启动原理

i.MX6ULL使用HABv4实现安全启动，通过数字签名验证确保启动镜像的完整性和真实性。

### 安全启动流程图（适配微信阅读）

```mermaid
flowchart LR
    A[Boot ROM启动] --> B[HAB Fuse检查]
    B --> C[验证SRK Hash]
    C --> D[加载U-Boot]
    D --> E[验证U-Boot签名]
    E --> F[加载Kernel+DTB]
    F --> G[验证统一签名]
    G --> H[启动Linux系统]
```

### 镜像签名流程图

```mermaid
flowchart LR
    A[原始镜像] --> B[4KB对齐填充]
    B --> C[生成IVT]
    C --> D[组合镜像+IVT]
    D --> E[创建统一CSF]
    E --> F[CST工具签名]
    F --> G[生成签名镜像]

    H[证书生成] --> I[SRK表]
    H --> J[CSF证书]
    H --> K[IMG证书]
    I --> E
    J --> E
    K --> E
```

## 2. 环境准备

### 2.1 工具下载

1. **CST工具**（Code Signing Tool）
   - 从NXP官网下载最新版本的CST工具
   - 解压到工作目录：`/opt/cst-3.3.1/`

2. **交叉编译工具链**
   ```bash
   # 安装ARM交叉编译工具链
   sudo apt-get install gcc-arm-linux-gnueabihf
   ```

3. **必要的软件包**
   ```bash
   sudo apt-get install openssl libssl-dev build-essential
   ```

### 2.2 目录结构

```
secure_boot_workspace/
├── cst-3.3.1/                 # CST工具目录
├── keys/                      # 证书和密钥目录
├── images/                    # 镜像文件目录
├── scripts/                   # 脚本目录
└── output/                    # 输出目录
```

## 3. 证书和密钥生成

### 3.1 生成PKI证书树

```bash
cd cst-3.3.1/keys/
./hab4_pki_tree.sh

# 生成的证书文件：
# - SRK_1_2_3_4_table.bin        # SRK表
# - SRK_1_2_3_4_fuse.bin         # SRK熔丝数据
# - CSF1_1_sha256_4096_65537_v3_usr_crt.pem  # CSF证书
# - IMG1_1_sha256_4096_65537_v3_usr_crt.pem  # IMG证书
# - CSF1_1_sha256_4096_65537_v3_usr_key.pem  # CSF私钥
# - IMG1_1_sha256_4096_65537_v3_usr_key.pem  # IMG私钥
```

### 3.2 证书验证

```bash
# 验证证书链
openssl verify -CAfile ../crts/SRK1_sha256_4096_65537_v3_ca_crt.pem \
    CSF1_1_sha256_4096_65537_v3_usr_crt.pem

openssl verify -CAfile CSF1_1_sha256_4096_65537_v3_usr_crt.pem \
    IMG1_1_sha256_4096_65537_v3_usr_crt.pem
```

## 4. U-Boot配置和编译

### 4.1 U-Boot配置

```bash
# 配置U-Boot支持HAB
make mx6ull_14x14_evk_defconfig
make menuconfig

# 在menuconfig中启用：
# ARM architecture -> Support i.MX HAB features
# ARM architecture -> Support SDP image mode
```

### 4.2 编译U-Boot

```bash
export ARCH=arm
export CROSS_COMPILE=arm-linux-gnueabihf-
make -j$(nproc)

# 生成文件：
# - u-boot.bin
# - u-boot.imx
```

### 4.3 U-Boot签名

#### 4.3.1 创建U-Boot CSF文件

```bash
cat > csf_uboot.txt << 'EOF'
[Header]
    Version = 4.2
    Hash Algorithm = sha256
    Engine Configuration = 0
    Certificate Format = X509
    Signature Format = CMS
    Engine = CAAM

[Install SRK]
    File = "SRK_1_2_3_4_table.bin"
    Source index = 0

[Install CSFK]
    File = "CSF1_1_sha256_4096_65537_v3_usr_crt.pem"

[Authenticate CSF]

[Install Key]
    Verification index = 0
    Target Index = 2
    File = "IMG1_1_sha256_4096_65537_v3_usr_crt.pem"

[Authenticate Data]
    Verification index = 2
    Blocks = 0x87800000 0x0 0x6F000 "u-boot.imx"
EOF
```

#### 4.3.2 生成U-Boot签名

```bash
# 生成CSF二进制文件
../linux64/bin/cst -i csf_uboot.txt -o csf_uboot.bin

# 创建签名的U-Boot
cat u-boot.imx csf_uboot.bin > u-boot-signed.imx
```

## 5. Kernel和DTB统一签名 ⭐

### 5.1 准备镜像文件

```bash
# 复制kernel和DTB到工作目录
cp /path/to/zImage ./
cp /path/to/imx6ull-14x14-evk.dtb ./

# 计算镜像大小和对齐
KERNEL_SIZE=$(stat -c%s zImage)
KERNEL_ALIGNED=$(((KERNEL_SIZE + 4095) & ~4095))
DTB_SIZE=$(stat -c%s imx6ull-14x14-evk.dtb)
DTB_ALIGNED=$(((DTB_SIZE + 4095) & ~4095))

echo "Kernel size: $KERNEL_SIZE, aligned: $KERNEL_ALIGNED"
echo "DTB size: $DTB_SIZE, aligned: $DTB_ALIGNED"
```

### 5.2 创建统一镜像（关键步骤）

```bash
# 创建4KB对齐的kernel镜像
cp zImage zImage-padded.bin
dd if=/dev/zero bs=1 count=$((KERNEL_ALIGNED - KERNEL_SIZE)) >> zImage-padded.bin

# 创建4KB对齐的DTB镜像
cp imx6ull-14x14-evk.dtb dtb-padded.bin
dd if=/dev/zero bs=1 count=$((DTB_ALIGNED - DTB_SIZE)) >> dtb-padded.bin

# 关键：将kernel和DTB合并为一个统一镜像
cat zImage-padded.bin dtb-padded.bin > kernel_dtb_combined.bin

# 计算合并后的总大小
COMBINED_SIZE=$((KERNEL_ALIGNED + DTB_ALIGNED))
echo "Combined image size: $COMBINED_SIZE (0x$(printf '%X' $COMBINED_SIZE))"
```

### 5.3 生成统一IVT

#### 5.3.1 修改genIVT.pl脚本（通用版本）

```perl
#! /usr/bin/perl -w
use strict;

if (@ARGV != 3) {
    print "Usage: $0 <load_address> <ivt_offset> <output_file>\n";
    exit 1;
}

my ($load_addr_str, $ivt_offset_str, $output_file) = @ARGV;
my $load_addr = hex($load_addr_str);
my $ivt_offset = hex($ivt_offset_str);
my $self_addr = $load_addr + $ivt_offset;
my $csf_addr = $self_addr + 0x20;

open(my $out, '>:raw', $output_file) or die "Unable to open: $!";
print $out pack("V", 0x412000D1); # Signature
print $out pack("V", $load_addr);  # Load Address
print $out pack("V", 0x0); # Reserved
print $out pack("V", 0x0); # DCD pointer
print $out pack("V", 0x0); # Boot Data
print $out pack("V", $self_addr); # Self Pointer
print $out pack("V", $csf_addr);  # CSF Pointer
print $out pack("V", 0x0); # Reserved
close($out);
```

#### 5.3.2 为统一镜像生成IVT

```bash
# 为合并镜像生成IVT（IVT放在合并镜像的末尾）
perl genIVT.pl 0x80800000 $(printf "0x%X" $COMBINED_SIZE) ivt_combined.bin

# 组合：合并镜像 + IVT
cat kernel_dtb_combined.bin ivt_combined.bin > kernel_dtb_combined_ivt.bin

echo "统一镜像+IVT生成完成"
ls -la kernel_dtb_combined_ivt.bin
```

### 5.4 创建统一CSF文件 ⭐

#### 5.4.1 统一CSF配置（关键改进）

```bash
cat > csf_kernel_dtb_combined.txt << EOF
[Header]
    Version = 4.2
    Hash Algorithm = sha256
    Engine Configuration = 0
    Certificate Format = X509
    Signature Format = CMS
    Engine = CAAM

[Install SRK]
    File = "SRK_1_2_3_4_table.bin"
    Source index = 0

[Install CSFK]
    File = "CSF1_1_sha256_4096_65537_v3_usr_crt.pem"

[Authenticate CSF]

[Install Key]
    Verification index = 0
    Target Index = 2
    File = "IMG1_1_sha256_4096_65537_v3_usr_crt.pem"

[Authenticate Data]
    Verification index = 2
    # 统一验证：kernel从0x80800000开始，DTB从0x83000000开始
    Blocks = 0x80800000 0x00000000 $(printf "0x%08X" $KERNEL_ALIGNED) "kernel_dtb_combined_ivt.bin", \\
             0x83000000 $(printf "0x%08X" $KERNEL_ALIGNED) $(printf "0x%08X" $DTB_ALIGNED) "kernel_dtb_combined_ivt.bin"
EOF

echo "统一CSF文件创建完成"
```

#### 5.4.2 CSF参数说明

```bash
# CSF中的Blocks参数解释：
# 第一行：0x80800000 0x00000000 KERNEL_SIZE "file"
#   - 0x80800000: kernel在DDR中的加载地址
#   - 0x00000000: 在文件中的偏移（从文件开头）
#   - KERNEL_SIZE: kernel的大小
#
# 第二行：0x83000000 KERNEL_OFFSET DTB_SIZE "file"
#   - 0x83000000: DTB在DDR中的加载地址
#   - KERNEL_OFFSET: DTB在文件中的偏移（kernel大小）
#   - DTB_SIZE: DTB的大小
```

### 5.5 生成统一签名镜像

```bash
# 使用CST工具生成统一签名
../linux64/bin/cst -i csf_kernel_dtb_combined.txt -o csf_combined.bin

# 创建最终的统一签名镜像
cat kernel_dtb_combined_ivt.bin csf_combined.bin > kernel_dtb_signed.bin

echo "=== 统一签名完成 ==="
ls -la kernel_dtb_signed.bin

# 验证签名镜像大小
SIGNED_SIZE=$(stat -c%s kernel_dtb_signed.bin)
echo "签名镜像大小: $SIGNED_SIZE bytes (0x$(printf '%X' $SIGNED_SIZE))"
```

### 5.6 镜像布局验证

```bash
# 验证镜像布局
echo "=== 镜像布局信息 ==="
echo "Kernel部分: 0x00000000 - 0x$(printf '%08X' $((KERNEL_ALIGNED - 1)))"
echo "DTB部分:   0x$(printf '%08X' $KERNEL_ALIGNED) - 0x$(printf '%08X' $((COMBINED_SIZE - 1)))"
echo "IVT位置:   0x$(printf '%08X' $COMBINED_SIZE)"
echo "CSF位置:   0x$(printf '%08X' $((COMBINED_SIZE + 0x20)))"

# 检查IVT内容
hexdump -C kernel_dtb_signed.bin -s $COMBINED_SIZE -n 32
```

## 6. 镜像烧录和验证

### 6.1 烧录统一签名镜像

```bash
# 烧录U-Boot到NAND Flash
=> nand erase 0x0 0x200000
=> tftp 0x80800000 u-boot-signed.imx
=> nand write 0x80800000 0x0 ${filesize}

# 烧录统一签名镜像到NAND Flash
=> nand erase 0x500000 0x1000000
=> tftp 0x80800000 kernel_dtb_signed.bin
=> nand write 0x80800000 0x500000 ${filesize}
```

### 6.2 U-Boot中加载和验证

```bash
# 从NAND加载统一镜像到内存
=> nand read 0x80800000 0x500000 0x1000000

# 验证统一签名镜像（一次验证kernel+DTB）
=> hab_auth_img 0x80800000 $(printf "0x%X" $((COMBINED_SIZE + 0x20))) $(printf "0x%X" $COMBINED_SIZE)

# 检查HAB状态
=> hab_status

# 如果验证成功，启动系统
=> bootz 0x80800000 - 0x83000000
```

### 6.3 内存布局说明

```bash
# 内存布局：
# 0x80800000: Kernel起始地址
# 0x83000000: DTB起始地址（0x80800000 + KERNEL_ALIGNED）
# IVT位置:    0x80800000 + COMBINED_SIZE
# CSF位置:    IVT位置 + 0x20
```

### 6.3 验证结果分析

#### 6.3.1 成功验证的输出
```
Authenticate image from DDR location 0x80800000...
Secure boot disabled
HAB Configuration: 0xf0, HAB State: 0x66
No HAB Events Found!
```

#### 6.3.2 失败验证的输出
```
HAB Configuration: 0xf0, HAB State: 0x66
--------- HAB Event 1 -----------------
STS = HAB_FAILURE (0x33)
RSN = HAB_INV_SIGNATURE (0x18)
CTX = HAB_CTX_COMMAND (0xC0)
```

## 7. 生产环境部署

### 7.1 SRK Hash烧录

```bash
# 计算SRK Hash
hexdump -e '/4 "0x"' -e '/4 "%X""\n"' SRK_1_2_3_4_fuse.bin

# 在U-Boot中烧录SRK Hash（谨慎操作！）
=> fuse prog 3 0 0x12345678  # SRK Hash[31:0]
=> fuse prog 3 1 0x9ABCDEF0  # SRK Hash[63:32]
=> fuse prog 3 2 0x11223344  # SRK Hash[95:64]
=> fuse prog 3 3 0x55667788  # SRK Hash[127:96]
=> fuse prog 3 4 0x99AABBCC  # SRK Hash[159:128]
=> fuse prog 3 5 0xDDEEFF00  # SRK Hash[191:160]
=> fuse prog 3 6 0x12345678  # SRK Hash[223:192]
=> fuse prog 3 7 0x9ABCDEF0  # SRK Hash[255:224]
```

### 7.2 启用安全启动

```bash
# 烧录SEC_CONFIG熔丝启用安全启动（不可逆操作！）
=> fuse prog 4 2 0x2  # 启用Secure Boot
```

## 8. 调试和故障排除

### 8.1 常见错误及解决方法

#### 8.1.1 HAB_INV_SIGNATURE (0x18)
**原因**：签名验证失败
**解决方法**：
- 检查证书链是否正确
- 验证CSF文件中的证书路径
- 确认私钥与证书匹配

#### 8.1.2 HAB_INV_ASSERTION (0x0C)
**原因**：地址断言失败
**解决方法**：
- 检查CSF中的地址配置
- 验证IVT偏移计算是否正确
- 确认加载地址与实际地址一致

#### 8.1.3 Data Abort
**原因**：内存访问越界
**解决方法**：
- 检查IVT偏移参数
- 验证镜像大小计算
- 确认内存地址范围

### 8.2 调试命令

```bash
# 查看HAB状态
=> hab_status

# 查看HAB事件
=> hab_failsafe

# 查看内存内容
=> md.b 0x80800000 0x100    # 查看镜像开头
=> md.b 0x80E6A000 0x40     # 查看IVT位置

# 查看熔丝状态
=> fuse read 3 0 8          # 读取SRK Hash
=> fuse read 4 2 1          # 读取SEC_CONFIG
```

## 9. 自动化脚本

### 9.1 统一签名自动化脚本

```bash
#!/bin/bash
# sign_kernel_dtb_unified.sh - 统一签名自动化脚本

set -e

WORKSPACE=$(pwd)
CST_PATH="../cst-3.3.1/linux64/bin/cst"
KEYS_PATH="../keys"

echo "=== i.MX6ULL统一签名自动化脚本 ==="

# 检查输入文件
if [ ! -f "zImage" ] || [ ! -f "imx6ull-14x14-evk.dtb" ]; then
    echo "错误：缺少zImage或DTB文件"
    exit 1
fi

# 计算镜像大小
KERNEL_SIZE=$(stat -c%s zImage)
KERNEL_ALIGNED=$(((KERNEL_SIZE + 4095) & ~4095))
DTB_SIZE=$(stat -c%s imx6ull-14x14-evk.dtb)
DTB_ALIGNED=$(((DTB_SIZE + 4095) & ~4095))
COMBINED_SIZE=$((KERNEL_ALIGNED + DTB_ALIGNED))

echo "Kernel: $KERNEL_SIZE -> $KERNEL_ALIGNED"
echo "DTB: $DTB_SIZE -> $DTB_ALIGNED"
echo "Combined: $COMBINED_SIZE"

# 创建填充镜像
cp zImage zImage-padded.bin
dd if=/dev/zero bs=1 count=$((KERNEL_ALIGNED - KERNEL_SIZE)) >> zImage-padded.bin

cp imx6ull-14x14-evk.dtb dtb-padded.bin
dd if=/dev/zero bs=1 count=$((DTB_ALIGNED - DTB_SIZE)) >> dtb-padded.bin

# 创建统一镜像
cat zImage-padded.bin dtb-padded.bin > kernel_dtb_combined.bin

# 生成统一IVT
perl genIVT.pl 0x80800000 $(printf "0x%X" $COMBINED_SIZE) ivt_combined.bin

# 组合镜像和IVT
cat kernel_dtb_combined.bin ivt_combined.bin > kernel_dtb_combined_ivt.bin

# 创建统一CSF文件
create_unified_csf() {
cat > csf_kernel_dtb_combined.txt << EOF
[Header]
    Version = 4.2
    Hash Algorithm = sha256
    Engine Configuration = 0
    Certificate Format = X509
    Signature Format = CMS
    Engine = CAAM

[Install SRK]
    File = "SRK_1_2_3_4_table.bin"
    Source index = 0

[Install CSFK]
    File = "CSF1_1_sha256_4096_65537_v3_usr_crt.pem"

[Authenticate CSF]

[Install Key]
    Verification index = 0
    Target Index = 2
    File = "IMG1_1_sha256_4096_65537_v3_usr_crt.pem"

[Authenticate Data]
    Verification index = 2
    Blocks = 0x80800000 0x00000000 $(printf "0x%08X" $KERNEL_ALIGNED) "kernel_dtb_combined_ivt.bin", \\
             0x83000000 $(printf "0x%08X" $KERNEL_ALIGNED) $(printf "0x%08X" $DTB_ALIGNED) "kernel_dtb_combined_ivt.bin"
EOF
}

# 生成CSF并签名
create_unified_csf
$CST_PATH -i csf_kernel_dtb_combined.txt -o csf_combined.bin

# 创建最终统一签名镜像
cat kernel_dtb_combined_ivt.bin csf_combined.bin > kernel_dtb_signed.bin

echo "=== 统一签名完成 ==="
ls -la kernel_dtb_signed.bin

# 显示验证命令
echo "=== 验证命令 ==="
echo "nand read 0x80800000 0x500000 0x1000000"
echo "hab_auth_img 0x80800000 $(printf "0x%X" $((COMBINED_SIZE + 0x20))) $(printf "0x%X" $COMBINED_SIZE)"
echo "bootz 0x80800000 - 0x83000000"
```

## 10. 总结

本文档基于实际验证的方法，详细介绍了i.MX6ULL处理器HABv4安全启动的完整实现流程，采用**kernel和DTB统一签名**的方式，包括：

### 🎯 核心特点

1. **统一签名**：kernel和DTB合并为一个镜像进行签名，简化验证流程
2. **实践验证**：基于实际项目验证的方法和参数
3. **自动化脚本**：提供完整的自动化签名脚本
4. **微信适配**：流程图适配微信公众号阅读格式

### 🔧 关键技术要点

- **4KB对齐**：所有镜像必须4KB对齐
- **统一IVT**：为合并镜像生成统一的IVT
- **CSF配置**：使用Blocks参数同时验证kernel和DTB
- **内存布局**：正确配置kernel和DTB的加载地址

### ⚠️ 重要注意事项

- **密钥安全**：妥善保管私钥和证书文件
- **熔丝操作**：SRK Hash和SEC_CONFIG烧录是不可逆操作
- **充分测试**：在开发环境充分验证后再部署生产
- **版本管理**：建立完善的签名镜像版本控制

### 🚀 优势总结

相比分别签名的方式，统一签名具有以下优势：
- **简化流程**：一次验证完成kernel和DTB
- **减少错误**：避免分别验证时的参数配置错误
- **提高效率**：减少U-Boot启动时的验证时间
- **便于管理**：统一的镜像文件便于版本管理

通过遵循本指南，您可以成功在i.MX6ULL平台上实现安全可靠的HABv4统一签名启动功能。

---

**📝 文档说明**：本文档基于实际项目验证，确保所有步骤和参数的正确性。如有疑问，请参考NXP官方文档或联系技术支持。
```
